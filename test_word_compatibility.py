#!/usr/bin/env python3
"""
Test Word compatibility fixes for MathCapture Studio
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MathCaptureStudio

def test_word_compatibility():
    """Test the Word compatibility fix function"""
    
    # Create a MathCaptureStudio instance (without UI)
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    app = MathCaptureStudio()
    
    # Test the problematic LaTeX from your screenshot
    problematic_latex = r"\begin{array}{r}\Rightarrow&{140>\mu(25(\sqrt{3}))+50\left(\frac{1}{2}\right)}\end{array}"
    
    print("🔧 Testing Word Compatibility Fix")
    print("=" * 50)
    print(f"Original LaTeX: {problematic_latex}")
    print()
    
    # Apply the Word compatibility fix
    fixed_latex = app.fix_word_compatibility(problematic_latex)
    
    print(f"Fixed LaTeX: {fixed_latex}")
    print()
    
    # Test the full processing pipeline
    print("🔧 Testing Full Processing Pipeline")
    print("=" * 50)
    
    fully_processed = app.ai_universal_postprocessor(problematic_latex, "Mathematics")
    
    print(f"Fully Processed LaTeX: {fully_processed}")
    print()
    
    # Verify the fix
    if "\\begin{array}" not in fixed_latex and "\\begin{array}" not in fully_processed:
        print("✅ SUCCESS: Array environments removed")
    else:
        print("❌ FAILED: Array environments still present")
    
    if "&" not in fixed_latex and "&" not in fully_processed:
        print("✅ SUCCESS: Alignment characters removed")
    else:
        print("❌ FAILED: Alignment characters still present")
    
    print()
    print("🎯 Expected Word-compatible output:")
    print("140 > μ ⋅ (25√3) + 50 ⋅ (1/2)")
    print()
    print("📋 Copy this LaTeX to test in Word:")
    print(f"{fully_processed}")
    
    root.destroy()

if __name__ == "__main__":
    test_word_compatibility()
